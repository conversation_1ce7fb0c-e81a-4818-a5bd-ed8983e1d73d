"""
Enhanced Azure Exam Analyzer with Sequential Thinking Integration
Improves accuracy through multi-phase reasoning, consensus building, and validation.
"""

import subprocess
import time
import tkinter as tk
from tkinter import ttk
from pynput import keyboard
from pynput.keyboard import Key
import threading
import os
import base64
import requests
import json
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import tempfile
import shutil
from datetime import datetime
from openai import OpenAI
from collections import Counter
import re
from typing import Dict, List, Tuple, Optional


class SequentialThinkingAnalyzer:
    """Enhanced analyzer with sequential thinking capabilities."""
    
    def __init__(self, client, models):
        self.client = client
        self.models = models
        
    def create_chain_of_thought_prompt(self):
        """Enhanced prompt with explicit chain-of-thought reasoning."""
        return """You are an expert Azure Solutions Architect. Analyze this exam question using systematic chain-of-thought reasoning.

**CRITICAL: Follow this exact reasoning structure:**

**STEP 1: QUESTION ANALYSIS**
- What is the core problem being asked?
- What are the key requirements and constraints?
- What Azure services are relevant?

**STEP 2: OPTION EVALUATION**
For each option, think through:
- How does this option address the requirements?
- What are the technical implications?
- What are the pros and cons?
- Rate confidence (1-10) for this option

**STEP 3: REASONING VERIFICATION**
- Double-check your understanding of the question
- Verify each option against ALL requirements
- Identify any assumptions you're making
- Consider edge cases or special scenarios

**STEP 4: CONFIDENCE ASSESSMENT**
- Rate your overall confidence (1-10) in your analysis
- Identify any uncertainties or areas needing clarification
- Note if additional information would change your answer

**STEP 5: FINAL RECOMMENDATION**
- State your recommended answer with clear justification
- Explain why other options are less suitable
- Provide confidence score for your recommendation

**FORMAT YOUR RESPONSE EXACTLY AS:**
QUESTION_ANALYSIS: [your analysis]
OPTION_A_EVALUATION: [analysis] CONFIDENCE: [1-10]
OPTION_B_EVALUATION: [analysis] CONFIDENCE: [1-10]
OPTION_C_EVALUATION: [analysis] CONFIDENCE: [1-10]
OPTION_D_EVALUATION: [analysis] CONFIDENCE: [1-10]
REASONING_VERIFICATION: [verification]
OVERALL_CONFIDENCE: [1-10]
RECOMMENDED_ANSWER: [A/B/C/D]
JUSTIFICATION: [why this is the best answer]
"""

    def create_peer_review_prompt(self, original_analysis: str, peer_analysis: str):
        """Prompt for models to review each other's reasoning."""
        return f"""You are reviewing another expert's analysis of an Azure exam question. Your task is to:

1. **IDENTIFY STRENGTHS** in the peer analysis
2. **IDENTIFY WEAKNESSES** or potential errors
3. **CHALLENGE ASSUMPTIONS** that seem questionable
4. **SUGGEST IMPROVEMENTS** to the reasoning
5. **PROVIDE YOUR OWN PERSPECTIVE** on any disagreements

**PEER'S ANALYSIS TO REVIEW:**
{peer_analysis}

**YOUR ORIGINAL ANALYSIS:**
{original_analysis}

**PROVIDE STRUCTURED FEEDBACK:**
STRENGTHS: [what the peer got right]
WEAKNESSES: [potential issues or errors]
CHALLENGES: [questionable assumptions]
IMPROVEMENTS: [how to strengthen the analysis]
DISAGREEMENTS: [where you differ and why]
REVISED_CONFIDENCE: [1-10 for the peer's conclusion]
YOUR_FINAL_POSITION: [A/B/C/D with brief justification]
"""

    def create_consensus_prompt(self, all_analyses: List[str]):
        """Prompt for building consensus from multiple analyses."""
        analyses_text = "\n\n".join([f"ANALYSIS {i+1}:\n{analysis}" for i, analysis in enumerate(all_analyses)])
        
        return f"""You are synthesizing multiple expert analyses of an Azure exam question. Your task is to build the most accurate consensus answer.

**ALL EXPERT ANALYSES:**
{analyses_text}

**SYNTHESIS PROCESS:**
1. **IDENTIFY CONSENSUS POINTS** - Where do experts agree?
2. **ANALYZE DISAGREEMENTS** - Where do they differ and why?
3. **EVALUATE REASONING QUALITY** - Which arguments are strongest?
4. **WEIGH CONFIDENCE SCORES** - Consider expert confidence levels
5. **SYNTHESIZE BEST ANSWER** - Combine the strongest reasoning

**PROVIDE STRUCTURED SYNTHESIS:**
CONSENSUS_POINTS: [where experts agree]
DISAGREEMENT_ANALYSIS: [key differences and their validity]
STRONGEST_ARGUMENTS: [best reasoning from any expert]
WEAKEST_ARGUMENTS: [flawed reasoning to avoid]
SYNTHESIS_CONFIDENCE: [1-10]
FINAL_CONSENSUS_ANSWER: [A/B/C/D]
CONSENSUS_JUSTIFICATION: [why this answer synthesizes the best reasoning]
"""

    def create_validation_prompt(self, question_image_content, final_answer: str, reasoning: str):
        """Prompt for validating the final answer against the original question."""
        return f"""You are performing final validation of an Azure exam answer. Your task is to verify the answer is correct and complete.

**FINAL ANSWER TO VALIDATE:** {final_answer}
**REASONING PROVIDED:** {reasoning}

**VALIDATION CHECKLIST:**
1. **QUESTION ALIGNMENT** - Does the answer directly address what was asked?
2. **REQUIREMENT COVERAGE** - Are all stated requirements satisfied?
3. **TECHNICAL ACCURACY** - Is the Azure solution technically sound?
4. **COMPLETENESS** - Is anything missing from the solution?
5. **LOGICAL CONSISTENCY** - Is the reasoning internally consistent?

**VALIDATION RESULT:**
QUESTION_ALIGNMENT: [PASS/FAIL with explanation]
REQUIREMENT_COVERAGE: [PASS/FAIL with explanation]
TECHNICAL_ACCURACY: [PASS/FAIL with explanation]
COMPLETENESS: [PASS/FAIL with explanation]
LOGICAL_CONSISTENCY: [PASS/FAIL with explanation]
OVERALL_VALIDATION: [PASS/FAIL]
VALIDATION_CONFIDENCE: [1-10]
VALIDATED_ANSWER: [A/B/C/D or NEEDS_REVISION]
REVISION_NOTES: [if validation fails, what needs fixing]
"""

    async def analyze_with_sequential_thinking(self, image_content: List[Dict]) -> Dict:
        """Main sequential thinking analysis pipeline."""
        try:
            # Phase 1: Individual Chain-of-Thought Analysis
            print("🧠 Phase 1: Individual Chain-of-Thought Analysis...")
            individual_analyses = await self._phase1_individual_analysis(image_content)
            
            # Phase 2: Peer Review and Refinement
            print("🔍 Phase 2: Peer Review and Refinement...")
            refined_analyses = await self._phase2_peer_review(individual_analyses, image_content)
            
            # Phase 3: Consensus Building
            print("🤝 Phase 3: Consensus Building...")
            consensus_result = await self._phase3_consensus_building(refined_analyses, image_content)
            
            # Phase 4: Final Validation
            print("✅ Phase 4: Final Validation...")
            validation_result = await self._phase4_validation(consensus_result, image_content)
            
            return validation_result
            
        except Exception as e:
            print(f"❌ Sequential thinking pipeline failed: {e}")
            return {"error": str(e), "final_answer": "ERROR", "confidence": 0}

    async def _phase1_individual_analysis(self, image_content: List[Dict]) -> List[Dict]:
        """Phase 1: Each model performs individual chain-of-thought analysis."""
        analyses = []
        num_images = len([item for item in image_content if item.get("type") == "image_url"])

        print(f"🚀 Starting parallel analysis with {len(self.models)} models on {num_images} screenshot(s)...")

        for i, model in enumerate(self.models):
            try:
                print(f"🤔 Starting chain-of-thought analysis with {model} on {num_images} screenshot(s)...")

                message_content = [{"type": "text", "text": self.create_chain_of_thought_prompt()}] + image_content

                completion = self.client.chat.completions.create(
                    model=model,
                    messages=[{"role": "user", "content": message_content}],
                    max_tokens=4096,
                    temperature=0.1,  # Slightly higher for more creative reasoning
                )

                if completion.choices and completion.choices[0].message:
                    analysis_text = completion.choices[0].message.content.strip()
                    parsed_analysis = self._parse_analysis(analysis_text)
                    parsed_analysis["model"] = model
                    parsed_analysis["raw_text"] = analysis_text
                    analyses.append(parsed_analysis)
                    confidence = parsed_analysis.get('overall_confidence', 'N/A')
                    answer = parsed_analysis.get('recommended_answer', 'N/A')
                    print(f"✅ {model} analysis completed successfully (Answer: {answer}, Confidence: {confidence}/10)")
                else:
                    print(f"❌ {model} returned empty response")

            except Exception as e:
                print(f"❌ {model} failed: {str(e)}")

        print(f"⏱️ Phase 1 complete! {len(analyses)}/{len(self.models)} models succeeded")
        return analyses

    def _parse_analysis(self, analysis_text: str) -> Dict:
        """Parse structured analysis response."""
        parsed = {}
        
        # Extract key components using regex
        patterns = {
            'recommended_answer': r'RECOMMENDED_ANSWER:\s*([A-D])',
            'overall_confidence': r'OVERALL_CONFIDENCE:\s*(\d+)',
            'justification': r'JUSTIFICATION:\s*(.+?)(?=\n[A-Z_]+:|$)',
        }
        
        for key, pattern in patterns.items():
            match = re.search(pattern, analysis_text, re.IGNORECASE | re.DOTALL)
            if match:
                if key == 'overall_confidence':
                    parsed[key] = int(match.group(1))
                else:
                    parsed[key] = match.group(1).strip()
        
        return parsed

    async def _phase2_peer_review(self, analyses: List[Dict], image_content: List[Dict]) -> List[Dict]:
        """Phase 2: Models review each other's reasoning."""
        if len(analyses) < 2:
            print("⚠️ Phase 2 skipped - need at least 2 analyses for peer review")
            return analyses

        print(f"🔍 Starting peer review phase with {len(analyses)} models...")
        refined_analyses = []

        for i, analysis in enumerate(analyses):
            try:
                # Get peer analysis for review
                peer_analysis = analyses[(i + 1) % len(analyses)]

                print(f"🔍 {analysis['model']} reviewing {peer_analysis['model']}'s analysis...")

                review_prompt = self.create_peer_review_prompt(
                    analysis['raw_text'],
                    peer_analysis['raw_text']
                )

                message_content = [{"type": "text", "text": review_prompt}] + image_content

                completion = self.client.chat.completions.create(
                    model=analysis['model'],
                    messages=[{"role": "user", "content": message_content}],
                    max_tokens=3072,
                    temperature=0.1,
                )

                if completion.choices and completion.choices[0].message:
                    review_text = completion.choices[0].message.content.strip()
                    analysis['peer_review'] = review_text
                    refined_position = self._extract_final_position(review_text)
                    analysis['refined_position'] = refined_position
                    refined_analyses.append(analysis)
                    print(f"✅ {analysis['model']} peer review completed (Refined position: {refined_position or 'N/A'})")
                else:
                    print(f"❌ {analysis['model']} peer review returned empty response")
                    refined_analyses.append(analysis)  # Keep original if review fails

            except Exception as e:
                print(f"❌ {analysis['model']} peer review failed: {e}")
                refined_analyses.append(analysis)  # Keep original if review fails

        print(f"⏱️ Phase 2 complete! {len(refined_analyses)} peer reviews processed")
        return refined_analyses

    async def _phase3_consensus_building(self, analyses: List[Dict], image_content: List[Dict]) -> Dict:
        """Phase 3: Build consensus from all analyses."""
        try:
            # Use the most reliable model for consensus (Claude in this case)
            consensus_model = self.models[-1]  # Assuming Claude is last

            print(f"🤝 Building consensus from {len(analyses)} analyses using {consensus_model}...")

            # Show individual positions before consensus
            print("📊 Individual positions:")
            for analysis in analyses:
                model = analysis.get('model', 'Unknown')
                answer = analysis.get('recommended_answer', 'N/A')
                confidence = analysis.get('overall_confidence', 'N/A')
                print(f"   {model}: {answer} (confidence: {confidence}/10)")

            all_analysis_texts = [analysis['raw_text'] for analysis in analyses]
            consensus_prompt = self.create_consensus_prompt(all_analysis_texts)

            message_content = [{"type": "text", "text": consensus_prompt}] + image_content

            completion = self.client.chat.completions.create(
                model=consensus_model,
                messages=[{"role": "user", "content": message_content}],
                max_tokens=3072,
                temperature=0.05,  # Lower temperature for consensus
            )

            if completion.choices and completion.choices[0].message:
                consensus_text = completion.choices[0].message.content.strip()
                consensus_result = self._parse_consensus(consensus_text)
                consensus_result['consensus_model'] = consensus_model
                consensus_result['raw_consensus'] = consensus_text
                consensus_result['input_analyses'] = analyses

                final_answer = consensus_result.get('final_answer', 'N/A')
                confidence = consensus_result.get('confidence', 'N/A')
                print(f"✅ {consensus_model} consensus completed (Final answer: {final_answer}, Confidence: {confidence}/10)")
                return consensus_result
            else:
                raise ValueError("Consensus model returned empty response")

        except Exception as e:
            print(f"❌ {consensus_model} consensus building failed: {e}")
            print("🚨 FAILSAFE MODE: Using highest confidence individual analysis...")
            # Fallback to highest confidence individual analysis
            return self._fallback_to_best_individual(analyses)

    async def _phase4_validation(self, consensus_result: Dict, image_content: List[Dict]) -> Dict:
        """Phase 4: Validate the final answer."""
        try:
            validation_model = consensus_result.get('consensus_model', self.models[-1])
            final_answer = consensus_result.get('final_answer', 'N/A')

            print(f"✅ Validating final answer '{final_answer}' using {validation_model}...")

            validation_prompt = self.create_validation_prompt(
                image_content,
                consensus_result.get('final_answer', ''),
                consensus_result.get('justification', '')
            )

            message_content = [{"type": "text", "text": validation_prompt}] + image_content

            completion = self.client.chat.completions.create(
                model=validation_model,
                messages=[{"role": "user", "content": message_content}],
                max_tokens=2048,
                temperature=0.0,  # Lowest temperature for validation
            )

            if completion.choices and completion.choices[0].message:
                validation_text = completion.choices[0].message.content.strip()
                validation_result = self._parse_validation(validation_text)

                # Combine consensus and validation results
                final_result = {**consensus_result, **validation_result}
                final_result['validation_text'] = validation_text

                validation_status = validation_result.get('validation_status', 'N/A')
                validation_confidence = validation_result.get('validation_confidence', 'N/A')
                validated_answer = validation_result.get('validated_answer', final_answer)

                print(f"✅ {validation_model} validation completed")
                print(f"📋 Validation Status: {validation_status}")
                print(f"📊 Validation Confidence: {validation_confidence}/10")
                print(f"🎯 Validated Answer: {validated_answer}")

                return final_result
            else:
                print("⚠️ Validation returned empty response, using consensus result")
                return consensus_result

        except Exception as e:
            print(f"❌ {validation_model} validation failed: {e}")
            print("⚠️ Using consensus result without validation")
            return consensus_result

    def _extract_final_position(self, review_text: str) -> str:
        """Extract final position from peer review."""
        match = re.search(r'YOUR_FINAL_POSITION:\s*([A-D])', review_text, re.IGNORECASE)
        return match.group(1) if match else None

    def _parse_consensus(self, consensus_text: str) -> Dict:
        """Parse consensus building response."""
        parsed = {}

        patterns = {
            'final_answer': r'FINAL_CONSENSUS_ANSWER:\s*([A-D])',
            'confidence': r'SYNTHESIS_CONFIDENCE:\s*(\d+)',
            'justification': r'CONSENSUS_JUSTIFICATION:\s*(.+?)(?=\n[A-Z_]+:|$)',
        }

        for key, pattern in patterns.items():
            match = re.search(pattern, consensus_text, re.IGNORECASE | re.DOTALL)
            if match:
                if key == 'confidence':
                    parsed[key] = int(match.group(1))
                else:
                    parsed[key] = match.group(1).strip()

        return parsed

    def _parse_validation(self, validation_text: str) -> Dict:
        """Parse validation response."""
        parsed = {}

        patterns = {
            'validated_answer': r'VALIDATED_ANSWER:\s*([A-D]|NEEDS_REVISION)',
            'validation_confidence': r'VALIDATION_CONFIDENCE:\s*(\d+)',
            'validation_status': r'OVERALL_VALIDATION:\s*(PASS|FAIL)',
        }

        for key, pattern in patterns.items():
            match = re.search(pattern, validation_text, re.IGNORECASE)
            if match:
                if key == 'validation_confidence':
                    parsed[key] = int(match.group(1))
                else:
                    parsed[key] = match.group(1).strip()

        return parsed

    def _fallback_to_best_individual(self, analyses: List[Dict]) -> Dict:
        """Fallback to the highest confidence individual analysis."""
        if not analyses:
            return {"error": "No analyses available", "final_answer": "ERROR", "confidence": 0}

        best_analysis = max(analyses, key=lambda x: x.get('overall_confidence', 0))
        return {
            'final_answer': best_analysis.get('recommended_answer', 'ERROR'),
            'confidence': best_analysis.get('overall_confidence', 0),
            'justification': best_analysis.get('justification', 'Fallback to best individual analysis'),
            'fallback_used': True,
            'best_model': best_analysis.get('model', 'Unknown')
        }


class EnhancedAzureExamAnalyzer:
    """Enhanced Azure Exam Analyzer with Sequential Thinking capabilities."""

    def __init__(self):
        self.hotkey_detected = False
        self.send_screenshots_detected = False
        self.running = True
        self.listener = None
        self.root = None
        self.api_key = "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"

        # FIXED: Use real model names
        self.models = [
            "google/gemini-2.5-flash",
            "openai/gpt-5-mini",  # Fixed: Real GPT model
            "anthropic/claude-sonnet-4"
        ]

        # Initialize OpenAI client for OpenRouter
        self.client = OpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key=self.api_key,
        )

        # Initialize sequential thinking analyzer
        self.sequential_analyzer = SequentialThinkingAnalyzer(self.client, self.models)

        # Screenshot collection
        self.collected_screenshots = []
        self.screenshot_lock = threading.Lock()

        # ShareX directory monitoring (use same path as original)
        self.sharex_dir = Path("C:/Users/<USER>/Desktop/ShareX-18.0.1-portable/ShareX/Screenshots/2025-08")
        if not self.sharex_dir.exists():
            self.sharex_dir.mkdir(parents=True, exist_ok=True)

        # Enhanced mode toggle
        self.use_sequential_thinking = True  # Enable by default
        self.accuracy_threshold = 7  # Minimum confidence for accepting answers

        # Performance tracking
        self.answer_history = []
        self.model_performance = {model: {"correct": 0, "total": 0} for model in self.models}

        # Debounce mechanism for hotkeys
        self.last_hotkey_time = {}
        self.hotkey_debounce_delay = 1.0  # 1 second debounce

        # Create persistent root window
        self.setup_root()
        self.setup_file_monitoring()

    def toggle_sequential_thinking(self, enabled: bool = None):
        """Toggle sequential thinking mode on/off."""
        if enabled is None:
            self.use_sequential_thinking = not self.use_sequential_thinking
        else:
            self.use_sequential_thinking = enabled

        mode = "ENABLED" if self.use_sequential_thinking else "DISABLED"
        print(f"🧠 Sequential Thinking Mode: {mode}")

    def set_accuracy_threshold(self, threshold: int):
        """Set minimum confidence threshold for accepting answers."""
        self.accuracy_threshold = max(1, min(10, threshold))
        print(f"🎯 Accuracy threshold set to: {self.accuracy_threshold}/10")

    async def analyze_with_enhanced_accuracy(self, image_paths: List[str]) -> str:
        """Main analysis method with enhanced accuracy features."""
        try:
            print(f"🚀 Starting enhanced analysis of {len(image_paths)} screenshot(s)...")
            print(f"🧠 Sequential Thinking: {'ENABLED' if self.use_sequential_thinking else 'DISABLED'}")
            print(f"🎯 Accuracy Threshold: {self.accuracy_threshold}/10")

            # Prepare image content
            image_content = []
            for image_path in image_paths:
                with open(image_path, "rb") as image_file:
                    base64_image = base64.b64encode(image_file.read()).decode('utf-8')
                    image_content.append({
                        "type": "image_url",
                        "image_url": {"url": f"data:image/png;base64,{base64_image}"}
                    })

            if self.use_sequential_thinking:
                # Use sequential thinking pipeline
                result = await self.sequential_analyzer.analyze_with_sequential_thinking(image_content)
            else:
                # Use original parallel analysis (improved)
                result = await self._parallel_analysis_improved(image_content)

            # Check confidence threshold
            confidence = result.get('confidence', 0)
            final_answer = result.get('final_answer', 'ERROR')

            if confidence < self.accuracy_threshold and not final_answer.startswith('ERROR'):
                print(f"⚠️ Low confidence ({confidence}/10) - below threshold ({self.accuracy_threshold}/10)")
                print("🔄 Attempting additional verification...")

                # Try additional verification round
                verification_result = await self._additional_verification(image_content, result)
                if verification_result.get('confidence', 0) > confidence:
                    result = verification_result
                    print(f"✅ Verification improved confidence to {verification_result.get('confidence', 0)}/10")

            # Display results
            self._display_enhanced_results(result)

            # Track performance
            self._track_performance(result)

            return result.get('final_answer', 'ERROR')

        except Exception as e:
            error_msg = f"❌ Enhanced analysis failed: {str(e)}"
            print(f"🚨 {error_msg}")
            self.show_answer_bubble(error_msg)
            return error_msg

    async def _parallel_analysis_improved(self, image_content: List[Dict]) -> Dict:
        """Improved version of the original parallel analysis."""
        print("🔄 Running improved parallel analysis...")

        # Enhanced prompts for better accuracy
        enhanced_prompt = self._create_enhanced_prompt()
        message_content = [{"type": "text", "text": enhanced_prompt}] + image_content

        analyses = []

        # Run all models in parallel
        async def analyze_with_model(model):
            try:
                completion = self.client.chat.completions.create(
                    model=model,
                    messages=[{"role": "user", "content": message_content}],
                    max_tokens=4096,
                    temperature=0.1,  # Slightly higher for better reasoning
                )

                if completion.choices and completion.choices[0].message:
                    analysis_text = completion.choices[0].message.content.strip()
                    parsed = self.sequential_analyzer._parse_analysis(analysis_text)
                    parsed['model'] = model
                    parsed['raw_text'] = analysis_text
                    return parsed

            except Exception as e:
                print(f"❌ {model} failed: {e}")
                return None

        # Collect all analyses
        import asyncio
        tasks = [analyze_with_model(model) for model in self.models]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        analyses = [r for r in results if r is not None and not isinstance(r, Exception)]

        if not analyses:
            return {"error": "All models failed", "final_answer": "ERROR", "confidence": 0}

        # Use weighted voting based on confidence
        return self._weighted_consensus(analyses)

    def _create_enhanced_prompt(self) -> str:
        """Create enhanced prompt with better accuracy instructions."""
        return """You are an expert Azure Solutions Architect. Analyze this exam question with extreme precision and accuracy.

**ACCURACY REQUIREMENTS:**
- Read the question multiple times to ensure complete understanding
- Consider ALL requirements and constraints mentioned
- Evaluate each option against EVERY requirement
- Double-check your reasoning before concluding
- Be explicit about your confidence level

**ANALYSIS STRUCTURE:**
1. **QUESTION UNDERSTANDING**: Restate the problem in your own words
2. **REQUIREMENTS EXTRACTION**: List all explicit and implicit requirements
3. **OPTION ANALYSIS**: For each option, evaluate against ALL requirements
4. **CONFIDENCE SCORING**: Rate your confidence (1-10) for each option
5. **FINAL DECISION**: Choose the option that best satisfies ALL requirements

**RESPONSE FORMAT:**
QUESTION_UNDERSTANDING: [restate the problem]
REQUIREMENTS: [list all requirements]
OPTION_A_ANALYSIS: [detailed analysis] CONFIDENCE: [1-10]
OPTION_B_ANALYSIS: [detailed analysis] CONFIDENCE: [1-10]
OPTION_C_ANALYSIS: [detailed analysis] CONFIDENCE: [1-10]
OPTION_D_ANALYSIS: [detailed analysis] CONFIDENCE: [1-10]
OVERALL_CONFIDENCE: [1-10]
RECOMMENDED_ANSWER: [A/B/C/D]
JUSTIFICATION: [why this option is definitively the best]

**CRITICAL**: Only recommend an answer if you are highly confident (8+/10). If uncertain, state your concerns clearly."""

    def _weighted_consensus(self, analyses: List[Dict]) -> Dict:
        """Build consensus using weighted voting based on confidence."""
        if not analyses:
            return {"error": "No analyses available", "final_answer": "ERROR", "confidence": 0}

        # Weight votes by confidence
        vote_weights = {}
        total_weight = 0

        for analysis in analyses:
            answer = analysis.get('recommended_answer', '')
            confidence = analysis.get('overall_confidence', 1)

            if answer in vote_weights:
                vote_weights[answer] += confidence
            else:
                vote_weights[answer] = confidence
            total_weight += confidence

        # Find highest weighted answer
        if vote_weights:
            best_answer = max(vote_weights.keys(), key=lambda x: vote_weights[x])
            consensus_confidence = min(10, int(vote_weights[best_answer] / len(analyses)))

            # Get justification from highest confidence analysis with this answer
            best_justification = ""
            for analysis in analyses:
                if (analysis.get('recommended_answer') == best_answer and
                    analysis.get('overall_confidence', 0) >= consensus_confidence):
                    best_justification = analysis.get('justification', '')
                    break

            return {
                'final_answer': best_answer,
                'confidence': consensus_confidence,
                'justification': best_justification,
                'vote_distribution': vote_weights,
                'consensus_method': 'weighted_voting'
            }

        # Fallback to highest individual confidence
        best_analysis = max(analyses, key=lambda x: x.get('overall_confidence', 0))
        return {
            'final_answer': best_analysis.get('recommended_answer', 'ERROR'),
            'confidence': best_analysis.get('overall_confidence', 0),
            'justification': best_analysis.get('justification', ''),
            'consensus_method': 'highest_confidence_fallback'
        }

    async def _additional_verification(self, image_content: List[Dict], initial_result: Dict) -> Dict:
        """Perform additional verification when confidence is low."""
        try:
            print("🔍 Performing additional verification...")

            verification_prompt = f"""You are performing a critical verification of an Azure exam answer.

INITIAL ANALYSIS RESULT:
Answer: {initial_result.get('final_answer', 'N/A')}
Confidence: {initial_result.get('confidence', 0)}/10
Justification: {initial_result.get('justification', 'N/A')}

Your task is to:
1. Re-examine the question independently
2. Verify if the initial answer is correct
3. Identify any errors in the reasoning
4. Provide your own analysis and recommendation

VERIFICATION RESULT:
INDEPENDENT_ANALYSIS: [your fresh analysis]
INITIAL_ANSWER_VERIFICATION: [CORRECT/INCORRECT/UNCERTAIN]
ERROR_IDENTIFICATION: [any errors found]
YOUR_RECOMMENDED_ANSWER: [A/B/C/D]
VERIFICATION_CONFIDENCE: [1-10]
VERIFICATION_JUSTIFICATION: [your reasoning]
"""

            # Use the most reliable model for verification
            verification_model = self.models[-1]  # Claude
            message_content = [{"type": "text", "text": verification_prompt}] + image_content

            completion = self.client.chat.completions.create(
                model=verification_model,
                messages=[{"role": "user", "content": message_content}],
                max_tokens=3072,
                temperature=0.05,
            )

            if completion.choices and completion.choices[0].message:
                verification_text = completion.choices[0].message.content.strip()

                # Parse verification result
                patterns = {
                    'recommended_answer': r'YOUR_RECOMMENDED_ANSWER:\s*([A-D])',
                    'confidence': r'VERIFICATION_CONFIDENCE:\s*(\d+)',
                    'justification': r'VERIFICATION_JUSTIFICATION:\s*(.+?)(?=\n[A-Z_]+:|$)',
                    'verification_status': r'INITIAL_ANSWER_VERIFICATION:\s*(CORRECT|INCORRECT|UNCERTAIN)',
                }

                parsed = {}
                for key, pattern in patterns.items():
                    match = re.search(pattern, verification_text, re.IGNORECASE | re.DOTALL)
                    if match:
                        if key == 'confidence':
                            parsed[key] = int(match.group(1))
                        else:
                            parsed[key] = match.group(1).strip()

                parsed['verification_text'] = verification_text
                parsed['verification_performed'] = True

                return parsed

        except Exception as e:
            print(f"❌ Additional verification failed: {e}")

        return initial_result

    def _display_enhanced_results(self, result: Dict):
        """Display enhanced results with detailed information."""
        final_answer = result.get('final_answer', 'ERROR')
        confidence = result.get('confidence', 0)
        justification = result.get('justification', 'No justification provided')

        # Create detailed display text
        display_text = f"Answer: {final_answer}"

        if confidence > 0:
            display_text += f" (Confidence: {confidence}/10)"

        # Add method used
        method = result.get('consensus_method', result.get('method', 'sequential_thinking'))
        if method:
            display_text += f"\nMethod: {method.replace('_', ' ').title()}"

        # Add verification status if performed
        if result.get('verification_performed'):
            verification_status = result.get('verification_status', 'N/A')
            display_text += f"\nVerification: {verification_status}"

        # Show confidence warning if low
        if confidence < self.accuracy_threshold:
            display_text += f"\n⚠️ LOW CONFIDENCE (< {self.accuracy_threshold})"

        # Display the answer
        self.show_answer_bubble(display_text)

        # Console output
        print(f"🎯 FINAL ANSWER: {final_answer}")
        print(f"📊 CONFIDENCE: {confidence}/10")
        print(f"🔧 METHOD: {method}")
        if justification:
            print(f"💡 JUSTIFICATION: {justification[:200]}{'...' if len(justification) > 200 else ''}")

    def _track_performance(self, result: Dict):
        """Track performance metrics for continuous improvement."""
        self.answer_history.append({
            'timestamp': datetime.now(),
            'answer': result.get('final_answer', 'ERROR'),
            'confidence': result.get('confidence', 0),
            'method': result.get('consensus_method', result.get('method', 'unknown')),
            'models_used': result.get('models_used', self.models)
        })

        # Keep only last 100 answers
        if len(self.answer_history) > 100:
            self.answer_history = self.answer_history[-100:]

    def show_answer_bubble(self, answer):
        """Show an auto-expanding bubble at top-left that auto-disappears after 25 seconds."""
        def create_bubble():
            bubble = tk.Toplevel(self.root)
            bubble.title("Enhanced Azure Exam Answer")
            bubble.overrideredirect(True)
            bubble.configure(bg='#2d3748')

            # Enhanced styling
            label = tk.Label(bubble, text=answer, font=('Segoe UI', 11, 'bold'),
                           bg='#2d3748', fg='#e2e8f0', wraplength=400, justify='left',
                           padx=15, pady=12)
            label.pack()

            # Position at top-left
            bubble.geometry("+50+50")
            bubble.lift()
            bubble.attributes('-topmost', True)

            # Auto-close after 25 seconds
            bubble.after(25000, bubble.destroy)

        if self.root:
            self.root.after(0, create_bubble)

    # Add the remaining methods from the original class (setup_root, file monitoring, etc.)
    def setup_root(self):
        """Setup the persistent root window."""
        self.root = tk.Tk()
        self.root.withdraw()
        self.root.title("Enhanced Azure Exam Analyzer")

    def setup_file_monitoring(self):
        """Setup ShareX file monitoring."""
        from watchdog.observers import Observer
        from watchdog.events import FileSystemEventHandler

        # Check if ShareX directory exists
        if not self.sharex_dir.exists():
            print(f"⚠️ ShareX directory doesn't exist: {self.sharex_dir}")
            self.sharex_dir.mkdir(parents=True, exist_ok=True)
            print(f"📁 Created ShareX directory: {self.sharex_dir}")
        else:
            print(f"✓ Found ShareX directory: {self.sharex_dir}")

        class ShareXFileHandler(FileSystemEventHandler):
            def __init__(self, analyzer):
                self.analyzer = analyzer
                self.last_processed = None

            def on_created(self, event):
                if event.is_directory:
                    return
                file_path = event.src_path
                print(f"🔍 File detected: {file_path}")  # Debug info
                if file_path.lower().endswith(('.png', '.jpg', '.jpeg')):
                    print(f"📸 Image file detected: {os.path.basename(file_path)}")  # Debug info
                    if file_path != self.last_processed:
                        self.last_processed = file_path
                        threading.Thread(target=self.analyzer.process_new_image,
                                       args=(file_path,), daemon=True).start()
                else:
                    print(f"⚠️ Non-image file ignored: {os.path.basename(file_path)}")  # Debug info

        self.file_handler = ShareXFileHandler(self)
        self.file_observer = Observer()
        self.file_observer.schedule(self.file_handler, str(self.sharex_dir), recursive=False)
        self.file_observer.start()
        print(f"👁️ File monitoring started for: {self.sharex_dir}")

    def process_new_image(self, image_path):
        """Process new image from ShareX."""
        if not self.hotkey_detected:
            return

        try:
            time.sleep(0.5)  # Wait for file to be fully written

            with self.screenshot_lock:
                temp_path = tempfile.mktemp(suffix='.png')
                shutil.copy2(image_path, temp_path)
                self.collected_screenshots.append(temp_path)

                # Detailed feedback like original
                print(f"📸 Screenshot {len(self.collected_screenshots)} stored. Press Ctrl+X to analyze all screenshots.")

                try:
                    os.remove(image_path)
                    print(f"🗑️ Cleaned up original screenshot: {os.path.basename(image_path)}")
                except Exception as e:
                    print(f"⚠ Could not delete original image: {e}")

        except Exception as e:
            print(f"❌ Error storing image: {e}")

    def process_collected_screenshots(self):
        """Process all collected screenshots with enhanced analysis."""
        with self.screenshot_lock:
            print(f"🔍 DEBUG: Checking collected screenshots...")
            print(f"🔍 DEBUG: Screenshot count: {len(self.collected_screenshots)}")
            print(f"🔍 DEBUG: Hotkey detected flag: {self.hotkey_detected}")
            print(f"🔍 DEBUG: ShareX directory: {self.sharex_dir}")
            print(f"🔍 DEBUG: Directory exists: {self.sharex_dir.exists()}")

            if self.sharex_dir.exists():
                files_in_dir = list(self.sharex_dir.glob("*"))
                print(f"🔍 DEBUG: Files in ShareX directory: {len(files_in_dir)}")
                for file in files_in_dir[:5]:  # Show first 5 files
                    print(f"   - {file.name}")

            if not self.collected_screenshots:
                print("❌ No screenshots collected. Take some screenshots first with Ctrl+D.")
                print("💡 Make sure ShareX is saving screenshots to the monitored directory")
                return

            try:
                print(f"🔄 Processing {len(self.collected_screenshots)} screenshot(s) with enhanced accuracy...")
                print(f"🧠 Sequential Thinking: {'ENABLED' if self.use_sequential_thinking else 'DISABLED'}")
                print(f"🎯 Accuracy Threshold: {self.accuracy_threshold}/10")
                print("=" * 60)

                # Use async analysis
                import asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                try:
                    result = loop.run_until_complete(
                        self.analyze_with_enhanced_accuracy(self.collected_screenshots)
                    )
                finally:
                    loop.close()

                print("=" * 60)
                print("🧹 Cleaning up temporary files...")

                # Clean up screenshots
                cleaned_count = 0
                for screenshot_path in self.collected_screenshots:
                    try:
                        os.remove(screenshot_path)
                        cleaned_count += 1
                        print(f"🗑️ Cleaned up temp screenshot: {os.path.basename(screenshot_path)}")
                    except Exception as e:
                        print(f"⚠ Could not delete temp image: {e}")

                self.collected_screenshots.clear()
                print(f"✅ Cleanup complete! Removed {cleaned_count} temporary files")
                print("🔄 Ready for next question sequence (press Ctrl+D to start capturing)")

            except Exception as e:
                print(f"❌ Error processing collected screenshots: {e}")
                # Clean up on error
                for screenshot_path in self.collected_screenshots:
                    try:
                        os.remove(screenshot_path)
                    except:
                        pass
                self.collected_screenshots.clear()

    def on_key_press(self, key):
        """Handle key press events using character codes like the original."""
        try:
            if hasattr(key, 'char') and key.char:
                if key.char == '\x04':  # CTRL+D
                    print("🎯 Ctrl+D detected! Screenshot capture mode ACTIVATED")
                    self.hotkey_detected = True
                    print("📸 Screenshots will be automatically collected")
                    print("🔄 Press Ctrl+X when done to analyze all screenshots")
                elif key.char == '\x18':  # CTRL+X
                    print("🚀 Ctrl+X detected! Analyzing collected screenshots...")
                    self.send_screenshots_detected = True
                    if self.hotkey_detected:
                        self.hotkey_detected = False
                        threading.Thread(target=self.process_collected_screenshots, daemon=True).start()
                elif key.char == '\x14':  # CTRL+T
                    print("🧠 Ctrl+T detected! Toggling Sequential Thinking mode...")
                    self.toggle_sequential_thinking()
                elif key.char == '\x11':  # CTRL+Q
                    print("👋 Ctrl+Q detected! Exiting Enhanced Azure Exam Analyzer...")
                    self.running = False
                    if self.listener:
                        self.listener.stop()
                    if self.root:
                        self.root.quit()
                elif key.char == '\x03':  # CTRL+C
                    print("👋 Ctrl+C detected! Exiting Enhanced Azure Exam Analyzer...")
                    self.running = False
                    if self.listener:
                        self.listener.stop()
                    if self.root:
                        self.root.quit()
                    return False  # Stop the listener
        except Exception as e:
            pass

        # ESC key does nothing - only Ctrl+C should exit
        if key == keyboard.Key.esc:
            print("⚠️ ESC pressed - use Ctrl+C to exit")
            return None  # Continue running

    def launch_sharex_capture(self):
        """Launch ShareX region capture using CLI hotkey actions (like original)."""
        try:
            # Try the full path to ShareX first
            sharex_path = "C:/Users/<USER>/Desktop/ShareX-18.0.1-portable/ShareX.exe"
            if Path(sharex_path).exists():
                subprocess.Popen([sharex_path, '-RectangleRegion'])
                print("✓ ShareX rectangle region capture launched (full path)")
            else:
                # Fallback to PATH
                subprocess.Popen(['ShareX', '-RectangleRegion'])
                print("✓ ShareX rectangle region capture launched (PATH)")
        except Exception as e:
            print(f"❌ Error launching ShareX: {e}")
            print("💡 Please take screenshots manually and save to ShareX folder")
            print(f"💡 Expected ShareX path: C:/Users/<USER>/Desktop/ShareX-18.0.1-portable/ShareX.exe")

    def check_hotkey(self):
        """Check for hotkey detection and launch ShareX (like original)."""
        if self.hotkey_detected:
            self.hotkey_detected = False
            print("� Launching ShareX for Azure exam question capture...")
            self.launch_sharex_capture()

    def start_listener(self):
        """Start the enhanced keyboard listener."""
        print("Enhanced Azure Exam Analyzer - Sequential Thinking Integration")
        print("=" * 65)
        print("🎯 Press Ctrl+D to capture Azure exam question screenshots")
        print("🚀 Press Ctrl+X to analyze all collected screenshots with enhanced accuracy")
        print("🧠 Press Ctrl+T to toggle Sequential Thinking mode")
        print("❌ Press Ctrl+C to exit (ESC does nothing)")
        print()
        print(f"📁 Monitoring ShareX directory: {self.sharex_dir}")
        print(f"🤖 Models: {', '.join(self.models)}")
        print(f"🧠 Sequential Thinking: {'ENABLED' if self.use_sequential_thinking else 'DISABLED'}")
        print(f"🎯 Accuracy Threshold: {self.accuracy_threshold}/10")
        print()

        # Start keyboard listener (like original)
        self.listener = keyboard.Listener(on_press=self.on_key_press)
        self.listener.start()

        # Start hotkey checking loop (like original)
        self.check_hotkey()

        try:
            while self.running:
                # Check for hotkeys periodically
                self.check_hotkey()

                # Check for screenshot processing
                if self.send_screenshots_detected:
                    self.send_screenshots_detected = False
                    # Already handled in on_key_press, just reset flag

                # Update GUI if present
                if self.root:
                    self.root.update()

                time.sleep(0.1)

        except KeyboardInterrupt:
            print("\n👋 Keyboard interrupt received")
        finally:
            if hasattr(self, 'listener') and self.listener:
                self.listener.stop()
            if hasattr(self, 'file_observer') and self.file_observer:
                self.file_observer.stop()
                self.file_observer.join()
            print("🔄 Enhanced analyzer stopped")

    def print_performance_stats(self):
        """Print performance statistics."""
        if not self.answer_history:
            print("📊 No performance data available yet")
            return

        print("\n📊 PERFORMANCE STATISTICS")
        print("=" * 40)

        # Confidence distribution
        confidences = [entry['confidence'] for entry in self.answer_history if entry['confidence'] > 0]
        if confidences:
            avg_confidence = sum(confidences) / len(confidences)
            print(f"Average Confidence: {avg_confidence:.1f}/10")

            high_confidence = len([c for c in confidences if c >= 8])
            print(f"High Confidence Answers (8+): {high_confidence}/{len(confidences)} ({high_confidence/len(confidences)*100:.1f}%)")

        # Method distribution
        methods = [entry['method'] for entry in self.answer_history]
        method_counts = Counter(methods)
        print(f"Methods Used: {dict(method_counts)}")

        print("=" * 40)


def main():
    """Main function for the enhanced analyzer."""
    try:
        print("Enhanced Azure Exam Analyzer - Sequential Thinking Integration")
        print("=" * 65)

        # Check dependencies
        try:
            import watchdog, requests, openai
            print("✓ All dependencies found")
        except ImportError as e:
            print(f"✗ Missing dependency: {e.name}")
            print("Please run: pip install watchdog requests openai")
            return

        # Check ShareX
        try:
            subprocess.run(['ShareX', '--version'], capture_output=True, timeout=3, check=True)
            print("✓ ShareX found and accessible")
        except Exception:
            print("⚠ ShareX may not be in PATH or is not installed.")

        print()

        # Create and start analyzer
        analyzer = EnhancedAzureExamAnalyzer()

        try:
            analyzer.start_listener()
        except KeyboardInterrupt:
            print("\n👋 Exiting...")
        finally:
            # Print final stats
            analyzer.print_performance_stats()

    except Exception as e:
        print(f"❌ An unexpected error occurred: {e}")


if __name__ == "__main__":
    main()
